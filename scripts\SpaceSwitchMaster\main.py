import os

import maya.cmds as cmds
from maya import OpenMayaUI as omui
from maya.app.general.mayaMixin import MayaQWidgetDockableMixin

from PySide2 import QtCore, QtGui, QtWidgets
from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *

import sys

from . import ui, version
from .modules import worldSpace, worldLocation, aimSpace, aimString, physicsOverlap, specialBake, overlapper, rotationSpace


class UISetup_SpaceSwitchMaster(MayaQWidgetDockableMixin, QtWidgets.QWidget):

    def __init__(self, parent=None):
        super().__init__()

        self.initUI()
        self.special_bake()
        self.world_space_switch()
        self.rotation_space_switch()
        self.world_location()
        self.aim_master()
        self.aim_space()
        self.aim_string()
        self.overlapper()
        self.physics_overlap()


    def initUI(self):

        self.setWindowTitle(version.Name)
        self.setGeometry(100, 100, 300, 400)

        # Create Main Layout and Main Widget
        self.mainLayout = QVBoxLayout()

        self.Widget_SpaceSwitch = QWidget()
        Widget_ExportZone = QWidget()

        self.mainLayout.addWidget(self.Widget_SpaceSwitch)
        self.mainLayout.addStretch()
        self.mainLayout.addSpacing(10)

        self.Secondary_Layout_SpaceSwitch = QVBoxLayout(self.Widget_SpaceSwitch)



        # Create window 
        self.setLayout(self.mainLayout)

    def special_bake(self):
        Layout_SpecialBake = QHBoxLayout()
        ui.new_layout(self.Secondary_Layout_SpaceSwitch, Layout_SpecialBake, "Special Bake")

        self.Spinbox_Number = QSpinBox()
        self.Spinbox_Number.setValue(1)
        self.Spinbox_Number.setMinimum(1)
        Layout_SpecialBake.addWidget(self.Spinbox_Number)

        Button_Bake = QPushButton("Bake")
        Layout_SpecialBake.addWidget(Button_Bake)

        Button_Bake.clicked.connect(self.launch_special_bake)

    def world_space_switch(self):
        Layout_WorldSpace = QHBoxLayout()
        ui.new_layout(self.Secondary_Layout_SpaceSwitch, Layout_WorldSpace, "World Space Switch")

        Button_Instant_WorldSpace = QPushButton("Instant World Space")
        Layout_WorldSpace.addWidget(Button_Instant_WorldSpace)

        Button_WorldSpace = QPushButton("World space switch")
        Layout_WorldSpace.addWidget(Button_WorldSpace)

        Button_Instant_WorldSpace.clicked.connect(worldSpace.instant_world_space)
        Button_WorldSpace.clicked.connect(worldSpace.world_space)

    def rotation_space_switch(self):
        Layout_RotationSpace = QHBoxLayout()
        ui.new_layout(self.Secondary_Layout_SpaceSwitch, Layout_RotationSpace, "Rotation Space Switch", Separator = False)

        Button_Instant_RotationSpace = QPushButton("Instant Rotation Space")
        Layout_RotationSpace.addWidget(Button_Instant_RotationSpace)

        Button_RotationSpace = QPushButton("Rotation space switch")
        Layout_RotationSpace.addWidget(Button_RotationSpace)

        Button_Instant_RotationSpace.clicked.connect(rotationSpace.instant_rotation_space)
        Button_RotationSpace.clicked.connect(rotationSpace.rotation_space)

    def world_location(self):
        Layout_WorldLocation = QHBoxLayout()
        ui.new_layout(self.Secondary_Layout_SpaceSwitch, Layout_WorldLocation, "World Location")

        Button_InstantWorldLocation = QPushButton("Instant world location")
        Layout_WorldLocation.addWidget(Button_InstantWorldLocation)
        Button_InstantWorldLocation.clicked.connect(worldLocation.InstantWorldLocation)

        Button_WorldLoction = QPushButton("World location")
        Layout_WorldLocation.addWidget(Button_WorldLoction)
        Button_WorldLoction.clicked.connect(worldLocation.WorldLocation)


#################

    def aim_master(self):
        self.Secondary_Layout_SpaceSwitch.addWidget(ui.SeparatorWidget())
        ui.spacer(self.Secondary_Layout_SpaceSwitch)

        Layout_Aim_Master = QVBoxLayout()
        ui.new_layout(self.Secondary_Layout_SpaceSwitch, Layout_Aim_Master, "Aim Master")
        

        self.Aim_Spinbox_Number = QSpinBox()
        self.Aim_Spinbox_Number.setValue(1)
        self.Aim_Spinbox_Number.setMinimum(1)
        Layout_Aim_Master.addWidget(self.Aim_Spinbox_Number)


        Layout_ComboBox = QHBoxLayout()
        Layout_Aim_Master.addLayout(Layout_ComboBox)

        self.Aim_Check_Translate = QCheckBox()
        self.Aim_Check_Translate.setText("Translate?")
        Layout_ComboBox.addWidget(self.Aim_Check_Translate)
        self.Aim_ComboBox_Aim = QComboBox()
        self.Aim_ComboBox_Aim.addItems(["+X", "+Y", "+Z", "-X", "-Y", "-Z"])
        Layout_ComboBox.addWidget(self.Aim_ComboBox_Aim)
        self.Aim_ComboBox_Up = QComboBox()
        self.Aim_ComboBox_Up.addItems(["+X", "+Y", "+Z", "-X", "-Y", "-Z"])
        self.Aim_ComboBox_Up.setCurrentIndex(1)
        Layout_ComboBox.addWidget(self.Aim_ComboBox_Up)
        self.Aim_Check_Bake = QCheckBox()
        self.Aim_Check_Bake.setText("Bake?")
        Layout_ComboBox.addWidget(self.Aim_Check_Bake)
        
    def aim_space(self):
        self.Secondary_Layout_SpaceSwitch.addWidget(ui.SeparatorWidget())
        
        Layout_AimSpace = QHBoxLayout()
        self.Secondary_Layout_SpaceSwitch.addLayout(Layout_AimSpace)


        Button_AimSpace = QPushButton("Aim space switch")
        Layout_AimSpace.addWidget(Button_AimSpace)
        Button_AimSpace.clicked.connect(self.launch_aimspace)


    def aim_string(self):
        self.Secondary_Layout_SpaceSwitch.addWidget(ui.SeparatorWidget())

        Layout_AimString = QHBoxLayout()
        self.Secondary_Layout_SpaceSwitch.addLayout(Layout_AimString)

        Button_LocatorCreation = QPushButton("Aim String")
        Layout_AimString.addWidget(Button_LocatorCreation)
        Button_LocatorCreation.clicked.connect(self.launch_aimstring)


    def overlapper(self):     
        self.Secondary_Layout_SpaceSwitch.addWidget(ui.SeparatorWidget())

        Layout_Overlapper = QHBoxLayout()
        self.Secondary_Layout_SpaceSwitch.addLayout(Layout_Overlapper)
        Layout_second = QVBoxLayout()
        Layout_Overlapper.addLayout(Layout_second)


        Button_Main = QPushButton("Overlap")
        Layout_second.addWidget(Button_Main)
        Button_Main.clicked.connect(self.launch_overlapper)





#########################

    def physics_overlap(self):
        self.Secondary_Layout_SpaceSwitch.addWidget(ui.SeparatorWidget())
        ui.spacer(self.Secondary_Layout_SpaceSwitch)

        Layout_PhysicsOverlap_Button = QHBoxLayout()
        Layout_PhysicsOverlap_Field = QHBoxLayout()
        ui.new_layout(self.Secondary_Layout_SpaceSwitch, Layout_PhysicsOverlap_Button, "Physics overlap")

        Button_PhysicsOverlap = QPushButton("Physics overlap")
        Layout_PhysicsOverlap_Button.addWidget(Button_PhysicsOverlap)

        GoalWeight = QLineEdit("0.62")
        GoalSmoothness = QLineEdit("2")
        
        Layout_PhysicsOverlap_Field.addWidget(QLabel("Weight:"))
        Layout_PhysicsOverlap_Field.addWidget(GoalWeight)
        Layout_PhysicsOverlap_Field.addWidget(QLabel("Smoothness:"))
        Layout_PhysicsOverlap_Field.addWidget(GoalSmoothness)
        
        self.Secondary_Layout_SpaceSwitch.addLayout(Layout_PhysicsOverlap_Field)

        Button_PhysicsOverlap.clicked.connect(lambda: physicsOverlap.run(GoalWeight.text(), GoalSmoothness.text()))


######################################

    def launch_special_bake(self):
        specialBake.run(self.Spinbox_Number.value())

    def launch_aimspace(self):
        aimSpace.run(self.Aim_ComboBox_Aim.currentText(), 
                     self.Aim_ComboBox_Up.currentText())
        
    def launch_aimstring(self):
        aimString.run(self.Aim_Check_Translate.isChecked(),
                      self.Aim_ComboBox_Aim.currentText(), 
                      self.Aim_ComboBox_Up.currentText())

    def launch_overlapper(self):
        overlapper.run(self.Aim_Check_Translate.isChecked(),
                       self.Aim_ComboBox_Aim.currentText(), 
                       self.Aim_ComboBox_Up.currentText(), 
                       self.Aim_Spinbox_Number.value(),
                       self.Aim_Check_Bake.isChecked())
        