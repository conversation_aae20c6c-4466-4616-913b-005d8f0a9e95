import maya.cmds as cmds
from . import utility

def instant_rotation_space():
	selection = cmds.ls(selection=True)

	for obj in selection:
		locator = cmds.spaceLocator(n=f"{obj}_Worldspace_Loc")
		cmds.scale(50,50,50, locator)

		locatorConstraint = cmds.parentConstraint(obj, locator, mo = False)
		cmds.delete(locatorConstraint)

		cmds.pointConstraint(obj, locator, mo=True)
		cmds.orientConstraint(locator, obj, mo=True)

def rotation_space():

	selection = cmds.ls(selection=True)

	start_time = cmds.playbackOptions(q=True, min=True)
	end_time = cmds.playbackOptions(q=True, max=True)

	for obj in selection:
		locator = cmds.spaceLocator(n=f"{obj}_Worldspace_Loc")
		cmds.scale(50,50,50, locator)

		locatorConstraint = cmds.parentConstraint(obj, locator, mo = False)
		cmds.bakeResults(locator, sm=True, t=(start_time, end_time))
		cmds.delete(locatorConstraint)

		cmds.pointConstraint(obj, locator, mo=True)
		cmds.orientConstraint(locator, obj, mo=True)