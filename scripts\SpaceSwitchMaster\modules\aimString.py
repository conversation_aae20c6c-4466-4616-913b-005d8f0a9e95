import maya.cmds as cmds
from . import utility


class run():

    List_Controller = []
    List_RootLocators = []
    List_OffsetLocators = []
    

    def __init__(self, DoTranslate, AimAxis, UpAxis):
        self.List_Controller.clear()
        self.List_OffsetLocators.clear()
        self.List_RootLocators.clear()
        self.TargetLocator = None

        self.setup_locator()
        self.constraint_controller(DoTranslate)
        self.aim_constraint(AimAxis, UpAxis)



    def setup_locator(self):

        List_ConstraintToDelete = []

        TimeStart = cmds.playbackOptions(q=True, min=True)
        TimeEnd = cmds.playbackOptions(q=True, max=True)

        self.List_Controller = cmds.ls(selection=True)

        for obj in self.List_Controller:
            Index = self.List_Controller.index(obj)

            RootLocator = cmds.spaceLocator(n=f"{obj}_AS_Loc_Root" + str(Index))[0]
            OffsetLocator = cmds.spaceLocator(n=f"{obj}_AS_Loc_Offset" + str(Index))[0]

            cmds.parent(OffsetLocator, RootLocator)
            
            List_ConstraintToDelete.append(cmds.parentConstraint(obj, RootLocator, mo=False))

            self.List_RootLocators.append(RootLocator)
            self.List_OffsetLocators.append(OffsetLocator)

        self.TargetLocator = cmds.spaceLocator(n=f"{obj}_AS_Loc_Target")
        cmds.matchTransform(self.TargetLocator, self.List_RootLocators[-1])
        cmds.move(10,0,0, self.TargetLocator, relative=True, objectSpace=True)
        List_ConstraintToDelete.append(cmds.parentConstraint(self.List_RootLocators[-1], self.TargetLocator, mo=True))


        cmds.bakeResults(*self.List_RootLocators, self.TargetLocator, sm=True, t=(TimeStart, TimeEnd))
        cmds.delete(*List_ConstraintToDelete)

    def constraint_controller(self, DoTranslate):

        for obj in self.List_Controller:
            Index = self.List_Controller.index(obj)

            if DoTranslate:
                    cmds.parentConstraint(self.List_OffsetLocators[Index], obj, mo=False)
            else:
                    cmds.orientConstraint(self.List_OffsetLocators[Index], obj, mo=False)


    def aim_constraint(self, AimAxis, UpAxis):

        Axis = utility.aim_vectors(AimAxis, UpAxis)

        for obj in self.List_OffsetLocators:
            Index = self.List_OffsetLocators.index(obj)
            Root = self.List_RootLocators[Index]

            if obj == self.List_OffsetLocators[-1]:
                cmds.aimConstraint(self.TargetLocator, obj, mo=True, aimVector=Axis[0], upVector=Axis[1], worldUpVector=(0,1,0), worldUpType="objectrotation", worldUpObject=Root)
            else:
                cmds.aimConstraint(self.List_RootLocators[Index + 1], obj, mo=True, aimVector=Axis[0], upVector=Axis[1], worldUpVector=(0,1,0), worldUpType="objectrotation", worldUpObject=Root)

