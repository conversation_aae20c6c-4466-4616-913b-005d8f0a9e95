import maya.cmds as cmds

def InstantWorldLocation():

	selection = cmds.ls(selection=True)

	for obj in selection:
		locator = cmds.spaceLocator(n=f"{obj}_WorldLocation_Loc")
		cmds.scale(30,30,30, locator)

		locatorConstraint = cmds.parentConstraint(obj, locator, mo = False)
		cmds.delete(locatorConstraint)
		

		
def WorldLocation():

	selection = cmds.ls(selection=True)

	start_time = cmds.playbackOptions(q=True, min=True)
	end_time = cmds.playbackOptions(q=True, max=True)

	for obj in selection:
		locator = cmds.spaceLocator(n=f"{obj}_WorldLocation_Loc")
		cmds.scale(30,30,30, locator)

		locatorConstraint = cmds.parentConstraint(obj, locator, mo = False)
		cmds.bakeResults(locator, sm=True, t=(start_time, end_time))
		cmds.delete(locatorConstraint)