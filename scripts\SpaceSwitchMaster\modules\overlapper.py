import maya.cmds as cmds
from . import utility


class run():

    List_Controller = []
    List_RootLocators = []
    List_OffsetLocators = []

    global Row_Settings
    global Row_Aim
    global Row_Up
    

    def __init__(self, WithTranslate, AimAxis, UpAxis, FrameOffset, WithBake, *kwargs):
        self.List_Controller.clear()
        self.List_OffsetLocators.clear()
        self.List_RootLocators.clear()
        self.TargetLocator = None

        self.setup_locator(AimAxis)
        self.constraint_controller(WithTranslate)
        self.aim_constraint(AimAxis, UpAxis)
        self.frame_offset(FrameOffset, WithBake)


    def setup_locator(self, AimAxis):

        List_ConstraintToDelete = []

        self.TimeStart = cmds.playbackOptions(q=True, min=True)
        self.TimeEnd = cmds.playbackOptions(q=True, max=True)

        self.List_Controller = cmds.ls(selection=True)

        for obj in self.List_Controller:
            Index = self.List_Controller.index(obj)

            RootLocator = cmds.spaceLocator(n="AS_Loc_Root" + str(Index))[0]
            OffsetLocator = cmds.spaceLocator(n="AS_Loc_Offset" + str(Index))[0]

            cmds.parent(OffsetLocator, RootLocator)
            
            List_ConstraintToDelete.append(cmds.parentConstraint(obj, RootLocator, mo=False))

            self.List_RootLocators.append(RootLocator)
            self.List_OffsetLocators.append(OffsetLocator)

        self.TargetLocator = cmds.spaceLocator(n="AS_Loc_Target")
        cmds.matchTransform(self.TargetLocator, self.List_RootLocators[-1])
        utility.move_from_axis(self.TargetLocator, AimAxis)
        List_ConstraintToDelete.append(cmds.parentConstraint(self.List_RootLocators[-1], self.TargetLocator, mo=True))


        cmds.bakeResults(*self.List_RootLocators, self.TargetLocator, sm=True, t=(self.TimeStart, self.TimeEnd))
        cmds.delete(*List_ConstraintToDelete)

    def constraint_controller(self, DoTranslate):

        for obj in self.List_Controller:
            Index = self.List_Controller.index(obj)

            if DoTranslate:
                    cmds.parentConstraint(self.List_OffsetLocators[Index], obj, mo=False)
            else:
                    cmds.orientConstraint(self.List_OffsetLocators[Index], obj, mo=False)


    def aim_constraint(self, Aim, Up):

        Axis = utility.aim_vectors(Aim, Up)

        for obj in self.List_OffsetLocators:
            Index = self.List_OffsetLocators.index(obj)
            Root = self.List_RootLocators[Index]

            if obj == self.List_OffsetLocators[-1]:
                cmds.aimConstraint(self.TargetLocator, obj, mo=True, aimVector=Axis[0], upVector=Axis[1], worldUpVector=(0,1,0), worldUpType="objectrotation", worldUpObject=Root)
            else:
                cmds.aimConstraint(self.List_RootLocators[Index + 1], obj, mo=True, aimVector=Axis[0], upVector=Axis[1], worldUpVector=(0,1,0), worldUpType="objectrotation", worldUpObject=Root)




    def frame_offset(self, FrameOffset, DoBake):
        TimeChange = int(FrameOffset)

        selection = self.List_RootLocators + self.TargetLocator

        for obj in selection:
            cmds.select(obj)
            Index = selection.index(obj)

            cmds.keyframe(edit = True, relative = True, timeChange = TimeChange * Index, time=(-100,1000))
            utility.set_infinty(selection)

        if DoBake:
            cmds.bakeResults(*self.List_Controller, sm=True, t=(self.TimeStart, self.TimeEnd))
            cmds.delete(selection)
        else:
            cmds.select(selection)
        

