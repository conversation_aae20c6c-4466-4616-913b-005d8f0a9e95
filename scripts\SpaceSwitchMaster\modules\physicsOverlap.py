import maya.cmds as cmds

def run(Weight, Smoothness):
    ConvertedWeight = float(Weight)
    ConvertedSmoothness = int(Smoothness)
    StartFrame = -50

    Selection = cmds.ls(selection=True)

    TimeStart = cmds.playbackOptions(q=True, min=True)
    TimeEnd = cmds.playbackOptions(q=True, max=True)

    LocatorProxy = cmds.spaceLocator(n="Loc_Proxy")
    ProxyConstraint = cmds.pointConstraint(Selection, LocatorProxy, mo=False)
    cmds.bakeResults(LocatorProxy, sm=True, t=(TimeStart, TimeEnd))
    cmds.delete(ProxyConstraint)

    Emitter = cmds.emitter(pos=(0, 0, 0), n='SSM_Emitter')
    Particle = cmds.particle(n='SSM_Particle')
    cmds.setAttr(Particle[0] + ".maxCount", 1)
    cmds.setAttr(Particle[0] + ".startFrame", StartFrame)
    cmds.connectDynamic(Particle, em=Emitter)

    cmds.matchTransform(Emitter, LocatorProxy, position=True)
    cmds.matchTransform(Particle, LocatorProxy, position=True)

    cmds.goal(Particle, goal=LocatorProxy, weight=ConvertedWeight)
    cmds.setAttr(Particle[0] + ".goalSmoothness", ConvertedSmoothness)

    LocatorTracker = cmds.spaceLocator(n="Loc_Tracker")
    cmds.connectAttr(Particle[0] + "Shape.worldCentroid", LocatorTracker[0] + ".translate", force=True)
    
    cmds.bakeResults(LocatorTracker, sm=True, t=(StartFrame, TimeEnd))

    cmds.pointConstraint(LocatorTracker, Selection)
    cmds.bakeResults(Selection, sm=True, t=(TimeStart, TimeEnd))

    cmds.delete(Emitter, Particle, LocatorProxy, LocatorTracker)