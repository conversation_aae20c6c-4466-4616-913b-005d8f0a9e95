import maya.cmds as cmds

def run(Number):

    StartTime = int(cmds.playbackOptions(q=True, min=True))
    EndTime = int(cmds.playbackOptions(q=True, max=True))
    FrameNumber = list(range(StartTime, EndTime))

    FrameToKeep = FrameNumber[0::Number]
    FrameToDelete = set(FrameNumber) - set(FrameToKeep)

    Selection = cmds.ls(selection = True)

    for obj in FrameToKeep:
        cmds.currentTime(obj)
        cmds.setKeyframe(Selection)

    for obj in FrameToDelete:
        cmds.cutKey(Selection, time=(obj, obj), clear=True)

    # print(FrameToDelete)