import maya.cmds as cmds


def constraint(<PERSON><PERSON>or, Obj):
    try:
        cmds.parentConstraint(<PERSON><PERSON><PERSON>, Obj, mo = True)
    except:
        try:
            cmds.pointConstraint(Locator, Obj, mo = True)
        except:
            try:
                cmds.orientConstraint(Locator, Obj, mo = True)
            except:
                print("Constraint error")


def aim_vectors(AimAxis, UpAxis):

    AimVector = [1,0,0]
    UpVector = [0,0,1]

    if AimAxis == "+X":
        AimVector = [1,0,0]
    elif AimAxis == "+Y":
        AimVector = [0,1,0]
    elif AimAxis == "+Z":
        AimVector = [0,0,1]
    elif AimAxis == "-X":
        AimVector = [-1,0,0]
    elif AimAxis == "-Y":
        AimVector = [0,-1,0]
    else:	
        AimVector = [0,0,-1]

    if UpAxis == "+X":
        UpVector = [1,0,0]
    elif UpAxis == "+Y":
        UpVector = [0,1,0]
    elif UpAxis == "+Z":
        UpVector = [0,0,1]
    elif UpAxis == "-X":
        UpVector = [-1,0,0]
    elif UpAxis == "-Y":
        UpVector = [0,-1,0]
    else:	
        UpVector = [0,0,-1]

    return AimVector, UpVector


def set_infinty(Selection):
    cmds.ls(Selection)
    cmds.setInfinity(pri="cycle", poi="cycle")
    cmds.select(cl=True)


def move_from_axis(obj, AimAxis):

    if AimAxis == "+X":
        cmds.move(100,0,0, obj, relative=True, objectSpace=True)
    elif AimAxis == "+Y":
        cmds.move(0,100,0, obj, relative=True, objectSpace=True)
    elif AimAxis == "+Z":
        cmds.move(0,0,100, obj, relative=True, objectSpace=True)
    elif AimAxis == "-X":
        cmds.move(-100,0,0, obj, relative=True, objectSpace=True)
    elif AimAxis == "-Y":
        cmds.move(0,-100,0, obj, relative=True, objectSpace=True)
    else:	
        cmds.move(0,0,-100, obj, relative=True, objectSpace=True)